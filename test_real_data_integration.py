#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实数据数字孪生系统集成测试脚本

功能：
1. 测试calibration_gt真实数据
2. 验证关键帧处理能力
3. 分析数字孪生ID分配效果
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

try:
    from src.core.digital_twin_controller import create_complete_controller
    print("✅ 成功导入数字孪生统一主控器")
except ImportError as e:
    print(f"❌ 导入数字孪生统一主控器失败: {e}")
    sys.exit(1)

class RealDataTester:
    """真实数据测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🔧 初始化真实数据测试器...")
        
        # 初始化数字孪生统一主控器
        try:
            self.digital_twin_controller = create_complete_controller()
            print("✅ 数字孪生统一主控器初始化完成")
        except Exception as e:
            print(f"❌ 数字孪生统一主控器初始化失败: {e}")
            raise
        
        # 测试统计
        self.test_stats = {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "processing_times": [],
            "card_counts": [],
            "test_results": []
        }
        
        # 测试数据路径
        self.calibration_gt_path = "legacy_assets/ceshi/calibration_gt"
    
    def load_json_annotation(self, json_path):
        """加载JSON标注文件"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"❌ 加载JSON文件失败 {json_path}: {e}")
            return None
    
    def convert_anylabeling_to_detections(self, annotation_data):
        """将AnyLabeling格式转换为数字孪生系统格式"""
        detections = []

        if not annotation_data or 'shapes' not in annotation_data:
            return detections

        for shape in annotation_data['shapes']:
            if shape.get('shape_type') != 'rectangle':
                continue

            # 获取标签
            label = shape.get('label', '')

            # 获取边界框坐标 - AnyLabeling使用4个点的格式
            points = shape.get('points', [])
            if len(points) != 4:
                continue

            # 计算边界框的最小和最大坐标
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            x1, x2 = min(x_coords), max(x_coords)
            y1, y2 = min(y_coords), max(y_coords)

            # 获取置信度和区域ID
            confidence = shape.get('score', 1.0)
            group_id = shape.get('group_id', 1)

            detection = {
                'label': label,
                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                'confidence': float(confidence),
                'group_id': int(group_id)
            }
            detections.append(detection)

        return detections
    
    def test_single_frame(self, frame_name):
        """测试单个帧"""
        print(f"\n🔍 测试帧: {frame_name}")
        
        # 构建文件路径
        image_path = os.path.join(self.calibration_gt_path, "images", f"{frame_name}.jpg")
        json_path = os.path.join(self.calibration_gt_path, "labels", f"{frame_name}.json")
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None
        
        if not os.path.exists(json_path):
            print(f"❌ 标注文件不存在: {json_path}")
            return None
        
        try:
            # 加载标注数据
            annotation_data = self.load_json_annotation(json_path)
            if not annotation_data:
                return None
            
            # 转换为检测格式
            detections = self.convert_anylabeling_to_detections(annotation_data)
            print(f"📊 从标注文件加载 {len(detections)} 个检测对象")
            
            # 显示检测对象的标签分布
            label_counts = {}
            for det in detections:
                label = det['label']
                label_counts[label] = label_counts.get(label, 0) + 1
            
            print(f"📊 标签分布: {dict(sorted(label_counts.items()))}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 数字孪生处理
            dt_result = self.digital_twin_controller.process_frame(detections)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 分析结果
            test_result = {
                "frame_name": frame_name,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "input_detection_count": len(detections),
                "dt_success": dt_result.success if dt_result else False,
                "processed_cards": len(dt_result.processed_cards) if dt_result and dt_result.success else 0,
                "strategy_used": dt_result.strategy_used if dt_result else "未知",
                "validation_errors": dt_result.validation_errors if dt_result else [],
                "validation_warnings": dt_result.validation_warnings if dt_result else [],
                "statistics": dt_result.statistics if dt_result and hasattr(dt_result, 'statistics') else {},
                "input_label_counts": label_counts
            }
            
            # 更新统计
            self.test_stats["total_tests"] += 1
            self.test_stats["processing_times"].append(processing_time)
            self.test_stats["card_counts"].append(test_result["processed_cards"])
            
            if test_result["dt_success"]:
                self.test_stats["successful_tests"] += 1
                print(f"✅ 数字孪生处理成功")
                print(f"📈 处理卡牌: {test_result['processed_cards']}张")
                print(f"📈 策略: {test_result['strategy_used']}")
                print(f"📈 耗时: {processing_time*1000:.1f}ms")
                
                # 分析处理模式
                stats = test_result.get('statistics', {})
                processing_mode = stats.get('processing_mode', '未知')
                print(f"📈 处理模式: {processing_mode}")
                
                if processing_mode == 'full_digital_twin':
                    print("🎯 完整数字孪生功能已启动")
                    # 显示数字孪生ID分配结果
                    if dt_result.processed_cards:
                        print("🎯 数字孪生ID分配结果:")
                        for i, card in enumerate(dt_result.processed_cards[:5]):  # 显示前5张
                            twin_id = card.get('twin_id', '未分配')
                            label = card.get('label', '未知')
                            group_id = card.get('group_id', '未知')
                            is_virtual = card.get('is_virtual', False)
                            virtual_flag = " (虚拟)" if is_virtual else ""
                            print(f"   {i+1}. {label} -> {twin_id}{virtual_flag} (区域{group_id})")
                        if len(dt_result.processed_cards) > 5:
                            print(f"   ... 还有{len(dt_result.processed_cards)-5}张卡牌")
                elif processing_mode == 'minimal_essential':
                    print("⚠️ 执行了最小必要处理（数字孪生未完全启动）")
                    activation_decision = stats.get('activation_decision', {})
                    reason = activation_decision.get('reason', '未知原因')
                    print(f"⚠️ 原因: {reason}")
                        
            else:
                self.test_stats["failed_tests"] += 1
                print(f"❌ 数字孪生处理失败")
                if test_result['validation_errors']:
                    print(f"❌ 验证错误: {test_result['validation_errors']}")
                if test_result['validation_warnings']:
                    print(f"⚠️ 验证警告: {test_result['validation_warnings']}")
            
            self.test_stats["test_results"].append(test_result)
            return test_result
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            self.test_stats["total_tests"] += 1
            self.test_stats["failed_tests"] += 1
            return None
    
    def test_key_frames(self):
        """测试关键帧"""
        print("\n🎯 开始测试关键帧...")
        
        # 根据测试素材文档选择的关键帧
        key_frames = [
            "frame_00000",  # 打鸟选择画面（无卡牌）
            "frame_00002",  # 牌局进行中，21张卡牌完全展开，启动数字孪生
            "frame_00003",  # 第21张卡牌流转，ID继承测试
            "frame_00005",  # 区域1与区域2互斥测试
            "frame_00025",  # 牌局进行中画面（有卡牌）
            "frame_00041",  # 小结算画面
            "frame_00247",  # 你输了小结算画面
            "frame_00371"   # 牌局结束画面
        ]
        
        for frame_name in key_frames:
            self.test_single_frame(frame_name)
            time.sleep(0.1)  # 短暂延迟
    
    def test_sequence_frames(self):
        """测试连续帧序列"""
        print("\n🎯 开始测试连续帧序列...")
        
        # 测试frame_00002到frame_00007的连续序列
        sequence_frames = [
            "frame_00002",  # 21张卡牌完全展开
            "frame_00003",  # 第21张卡牌流转
            "frame_00004",  # 动画效果，继承测试
            "frame_00005",  # 区域1与区域2互斥
            "frame_00006",  # 卡牌消失，补偿测试
            "frame_00007"   # 正常流转
        ]
        
        print("📋 这个序列将测试:")
        print("   - 数字孪生ID分配")
        print("   - 帧间继承机制")
        print("   - 区域互斥处理")
        print("   - 遮挡补偿机制")
        
        for frame_name in sequence_frames:
            self.test_single_frame(frame_name)
            time.sleep(0.1)  # 短暂延迟
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 生成测试报告...")
        
        # 计算统计指标
        total_tests = self.test_stats["total_tests"]
        success_rate = (self.test_stats["successful_tests"] / total_tests * 100) if total_tests > 0 else 0
        avg_processing_time = sum(self.test_stats["processing_times"]) / len(self.test_stats["processing_times"]) if self.test_stats["processing_times"] else 0
        avg_card_count = sum(self.test_stats["card_counts"]) / len(self.test_stats["card_counts"]) if self.test_stats["card_counts"] else 0
        
        report = {
            "test_summary": {
                "timestamp": datetime.now().isoformat(),
                "total_tests": total_tests,
                "successful_tests": self.test_stats["successful_tests"],
                "failed_tests": self.test_stats["failed_tests"],
                "success_rate": f"{success_rate:.1f}%",
                "average_processing_time": f"{avg_processing_time*1000:.1f}ms",
                "average_card_count": f"{avg_card_count:.1f}张"
            },
            "performance_metrics": {
                "min_processing_time": f"{min(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "max_processing_time": f"{max(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "total_cards_processed": sum(self.test_stats["card_counts"])
            },
            "detailed_results": self.test_stats["test_results"]
        }
        
        # 保存报告
        os.makedirs("output", exist_ok=True)
        report_path = "output/real_data_integration_test_report.json"

        # 自定义JSON编码器处理set类型
        def json_serializer(obj):
            if isinstance(obj, set):
                return list(obj)
            raise TypeError(f'Object of type {obj.__class__.__name__} is not JSON serializable')

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=json_serializer)
        
        print(f"✅ 测试报告已保存: {report_path}")
        
        # 打印摘要
        print("\n📊 测试摘要:")
        print(f"总测试数: {total_tests}")
        print(f"成功: {self.test_stats['successful_tests']}, 失败: {self.test_stats['failed_tests']}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均处理时间: {avg_processing_time*1000:.1f}ms")
        print(f"平均卡牌数: {avg_card_count:.1f}张")
        
        return report

def main():
    """主函数"""
    print("🚀 真实数据数字孪生系统集成测试")
    print("=" * 50)
    
    try:
        # 创建测试器
        tester = RealDataTester()
        
        # 测试关键帧
        tester.test_key_frames()
        
        # 测试连续帧序列
        tester.test_sequence_frames()
        
        # 生成测试报告
        report = tester.generate_test_report()
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
