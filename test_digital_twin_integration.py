#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数字孪生系统集成验证测试脚本

功能：
1. 验证统一主控器集成效果
2. 测试关键帧处理能力
3. 分析数字孪生处理结果
4. 生成详细的测试报告
"""

import os
import sys
import time
import json
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.core.digital_twin_controller import create_complete_controller
from ultralytics import YOLO

class DigitalTwinIntegrationTester:
    """数字孪生系统集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🔧 初始化数字孪生系统集成测试器...")
        
        # 初始化数字孪生统一主控器
        self.digital_twin_controller = create_complete_controller()
        print("✅ 数字孪生统一主控器初始化完成")
        
        # 初始化YOLO检测器
        self.detector = None
        self._load_detector()
        
        # 测试统计
        self.test_stats = {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "processing_times": [],
            "card_counts": [],
            "test_results": []
        }
        
        # 测试素材路径
        self.test_data_paths = {
            "key_frames": "legacy_assets/ceshi/tupian",
            "calibration_gt": "legacy_assets/ceshi/calibration_gt"
        }
    
    def _load_detector(self):
        """加载YOLO检测器"""
        try:
            model_paths = [
                "models/best.pt",
                "best.pt",
                "yolov8n.pt"  # 备用模型
            ]
            
            for model_path in model_paths:
                if os.path.exists(model_path):
                    self.detector = YOLO(model_path)
                    print(f"✅ 成功加载YOLO模型: {model_path}")
                    return
            
            print("⚠️ 未找到YOLO模型文件，将使用模拟数据")
            self.detector = None
            
        except Exception as e:
            print(f"❌ YOLO模型加载失败: {e}")
            self.detector = None
    
    def _format_detections_for_digital_twin(self, yolo_results):
        """将YOLO检测结果转换为数字孪生系统格式"""
        detections = []
        
        if self.detector is None:
            # 返回模拟数据用于测试
            return [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.95, 'group_id': 1},
                {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.90, 'group_id': 1},
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.88, 'group_id': 1}
            ]
        
        if hasattr(yolo_results, 'boxes') and yolo_results.boxes is not None:
            boxes = yolo_results.boxes
            
            for i in range(len(boxes)):
                # 获取边界框坐标
                box = boxes.xyxy[i].cpu().numpy()
                x1, y1, x2, y2 = box
                
                # 获取置信度
                confidence = float(boxes.conf[i].cpu().numpy())
                
                # 获取类别
                class_id = int(boxes.cls[i].cpu().numpy())
                label = self.detector.names[class_id] if self.detector else str(class_id)
                
                # 根据位置确定区域ID
                group_id = self._determine_group_id([x1, y1, x2, y2])
                
                detection = {
                    'label': label,
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'confidence': confidence,
                    'group_id': group_id
                }
                detections.append(detection)
        
        return detections
    
    def _determine_group_id(self, bbox):
        """根据边界框位置确定区域ID"""
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        
        # 简化的区域分配逻辑（基于640x320分辨率）
        if center_y < 160:  # 上半部分
            if center_x < 320:
                return 7  # 对战方区域
            else:
                return 8  # 对战方其他区域
        else:  # 下半部分
            if center_x < 320:
                return 1  # 观战方手牌区域
            else:
                return 4  # 观战方打牌区域
        
        return 1  # 默认区域
    
    def test_single_image(self, image_path):
        """测试单张图像"""
        print(f"\n🔍 测试图像: {image_path}")
        
        try:
            # 读取图像
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                return None
            
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法读取图像: {image_path}")
                return None
            
            # 记录开始时间
            start_time = time.time()
            
            # YOLO检测
            if self.detector is not None:
                yolo_results = self.detector(image)
                formatted_detections = self._format_detections_for_digital_twin(yolo_results)
            else:
                # 使用模拟数据
                formatted_detections = self._format_detections_for_digital_twin(None)
            
            print(f"📊 检测到 {len(formatted_detections)} 个对象")
            
            # 数字孪生处理
            dt_result = self.digital_twin_controller.process_frame(formatted_detections)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 分析结果
            test_result = {
                "image_path": image_path,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "detection_count": len(formatted_detections),
                "dt_success": dt_result.success if dt_result else False,
                "processed_cards": len(dt_result.processed_cards) if dt_result and dt_result.success else 0,
                "strategy_used": dt_result.strategy_used if dt_result else "未知",
                "validation_errors": dt_result.validation_errors if dt_result else [],
                "statistics": dt_result.statistics if dt_result and hasattr(dt_result, 'statistics') else {}
            }
            
            # 更新统计
            self.test_stats["total_tests"] += 1
            self.test_stats["processing_times"].append(processing_time)
            self.test_stats["card_counts"].append(test_result["processed_cards"])
            
            if test_result["dt_success"]:
                self.test_stats["successful_tests"] += 1
                print(f"✅ 数字孪生处理成功: {test_result['processed_cards']}张卡牌")
                print(f"📈 策略: {test_result['strategy_used']}, 耗时: {processing_time*1000:.1f}ms")
            else:
                self.test_stats["failed_tests"] += 1
                print(f"❌ 数字孪生处理失败: {test_result['validation_errors']}")
            
            self.test_stats["test_results"].append(test_result)
            return test_result
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            self.test_stats["total_tests"] += 1
            self.test_stats["failed_tests"] += 1
            return None
    
    def test_key_frames(self):
        """测试关键帧"""
        print("\n🎯 开始测试关键帧...")
        
        key_frames_dir = self.test_data_paths["key_frames"]
        if not os.path.exists(key_frames_dir):
            print(f"❌ 关键帧目录不存在: {key_frames_dir}")
            return
        
        # 获取关键帧文件
        key_frame_files = [
            "frame_00000.jpg",  # 打鸟选择画面（无卡牌）
            "frame_00025.jpg",  # 牌局进行中画面（有卡牌）
            "frame_00247.jpg",  # 你输了小结算画面（混合场景）
            "frame_00371.jpg"   # 牌局结束画面（无卡牌）
        ]
        
        for frame_file in key_frame_files:
            frame_path = os.path.join(key_frames_dir, frame_file)
            self.test_single_image(frame_path)
    
    def test_calibration_samples(self, sample_count=10):
        """测试calibration_gt样本"""
        print(f"\n🎯 开始测试calibration_gt样本 (前{sample_count}张)...")
        
        images_dir = os.path.join(self.test_data_paths["calibration_gt"], "images")
        if not os.path.exists(images_dir):
            print(f"❌ calibration_gt图像目录不存在: {images_dir}")
            return
        
        # 获取图像文件
        image_files = sorted([f for f in os.listdir(images_dir) if f.endswith('.jpg')])
        
        # 测试前N张图像
        for i, image_file in enumerate(image_files[:sample_count]):
            image_path = os.path.join(images_dir, image_file)
            self.test_single_image(image_path)
            
            # 显示进度
            print(f"📊 进度: {i+1}/{min(sample_count, len(image_files))}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 生成测试报告...")
        
        # 计算统计指标
        total_tests = self.test_stats["total_tests"]
        success_rate = (self.test_stats["successful_tests"] / total_tests * 100) if total_tests > 0 else 0
        avg_processing_time = np.mean(self.test_stats["processing_times"]) if self.test_stats["processing_times"] else 0
        avg_card_count = np.mean(self.test_stats["card_counts"]) if self.test_stats["card_counts"] else 0
        
        report = {
            "test_summary": {
                "timestamp": datetime.now().isoformat(),
                "total_tests": total_tests,
                "successful_tests": self.test_stats["successful_tests"],
                "failed_tests": self.test_stats["failed_tests"],
                "success_rate": f"{success_rate:.1f}%",
                "average_processing_time": f"{avg_processing_time*1000:.1f}ms",
                "average_card_count": f"{avg_card_count:.1f}张"
            },
            "performance_metrics": {
                "min_processing_time": f"{min(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "max_processing_time": f"{max(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "total_cards_processed": sum(self.test_stats["card_counts"])
            },
            "detailed_results": self.test_stats["test_results"]
        }
        
        # 保存报告
        report_path = "output/digital_twin_integration_test_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试报告已保存: {report_path}")
        
        # 打印摘要
        print("\n📊 测试摘要:")
        print(f"总测试数: {total_tests}")
        print(f"成功: {self.test_stats['successful_tests']}, 失败: {self.test_stats['failed_tests']}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均处理时间: {avg_processing_time*1000:.1f}ms")
        print(f"平均卡牌数: {avg_card_count:.1f}张")
        
        return report

def main():
    """主函数"""
    print("🚀 数字孪生系统集成验证测试")
    print("=" * 50)
    
    # 创建测试器
    tester = DigitalTwinIntegrationTester()
    
    # 测试关键帧
    tester.test_key_frames()
    
    # 测试calibration_gt样本
    tester.test_calibration_samples(sample_count=5)
    
    # 生成测试报告
    report = tester.generate_test_report()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
