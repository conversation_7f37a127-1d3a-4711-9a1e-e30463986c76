#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌检测主程序
功能：
1. 实时屏幕捕获
2. 卡牌检测
3. 结果可视化
"""

import os
import sys
import time
import json
import cv2
import numpy as np
from mss import mss
import argparse

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.state_builder import StateBuilder
from src.core.decision import DecisionMaker
# 🆕 导入数字孪生统一主控器
from src.core.digital_twin_controller import create_complete_controller

class PaohuziAI:
    """
    跑胡子AI主程序
    集成检测、状态转换和决策模块
    """
    
    def __init__(self, config_path=None):
        """
        初始化跑胡子AI
        
        Args:
            config_path (str): 配置文件路径
        """
        # 加载配置
        self.config = {}
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.json'):
                    self.config = json.load(f)
                elif config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    import yaml
                    self.config = yaml.safe_load(f)
                else:
                    print(f"不支持的配置文件格式: {config_path}")
        
        # 初始化组件
        self.state_builder = StateBuilder(config_path)
        self.decision_maker = DecisionMaker(self.config.get('decision', {}))

        # 🆕 初始化数字孪生统一主控器
        self.digital_twin_controller = create_complete_controller()
        print("✅ 数字孪生统一主控器初始化完成")
        
        # 屏幕捕获设置
        self.monitor = self.config.get('monitor', {'top': 0, 'left': 0, 'width': 640, 'height': 320})
        self.sct = mss()
        
        # 图像大小设置
        self.img_size = self.config.get('img_size', [640, 320])
        
        # 检测模型
        self.detector = None
        self._load_detector()
        
        # 性能统计
        self.fps = 0
        self.detection_time = 0
        self.decision_time = 0
        self.frame_count = 0
        self.last_time = time.time()

        # 🆕 数字孪生处理统计
        self.dt_processing_time = 0
        self.dt_success_count = 0
        self.dt_failure_count = 0
    
    def _load_detector(self):
        """加载检测模型"""
        try:
            from ultralytics import YOLO
            model_path = self.config.get('model_path', 'best.pt')
            if os.path.exists(model_path):
                self.detector = YOLO(model_path)
                print(f"成功加载模型: {model_path}")
            else:
                print(f"模型文件不存在: {model_path}")
                self.detector = None
        except ImportError:
            print("未安装ultralytics库，无法加载YOLO模型")
            self.detector = None

    def _format_detections_for_digital_twin(self, yolo_results):
        """
        将YOLO检测结果转换为数字孪生系统所需的格式

        Args:
            yolo_results: YOLO检测结果

        Returns:
            List[Dict]: 格式化后的检测数据
        """
        detections = []

        if hasattr(yolo_results, 'boxes') and yolo_results.boxes is not None:
            boxes = yolo_results.boxes

            for i in range(len(boxes)):
                # 获取边界框坐标
                box = boxes.xyxy[i].cpu().numpy()
                x1, y1, x2, y2 = box

                # 获取置信度
                confidence = float(boxes.conf[i].cpu().numpy())

                # 获取类别
                class_id = int(boxes.cls[i].cpu().numpy())
                label = self.detector.names[class_id] if self.detector else str(class_id)

                # 根据位置确定区域ID（简化版本）
                group_id = self._determine_group_id([x1, y1, x2, y2])

                detection = {
                    'label': label,
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'confidence': confidence,
                    'group_id': group_id
                }
                detections.append(detection)

        return detections

    def _determine_group_id(self, bbox):
        """
        根据边界框位置确定区域ID

        Args:
            bbox: [x1, y1, x2, y2] 边界框坐标

        Returns:
            int: 区域ID
        """
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        # 简化的区域分配逻辑（基于640x320分辨率）
        # 这里可以根据实际游戏界面布局进行调整
        if center_y < 160:  # 上半部分
            if center_x < 320:
                return 7  # 对战方区域
            else:
                return 8  # 对战方其他区域
        else:  # 下半部分
            if center_x < 320:
                return 1  # 观战方手牌区域
            else:
                return 4  # 观战方打牌区域

        return 1  # 默认区域
    
    def capture_screen(self):
        """
        捕获屏幕
        
        Returns:
            numpy.ndarray: 屏幕图像
        """
        sct_img = self.sct.grab(self.monitor)
        img = np.array(sct_img)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        
        # 调整图像大小
        if img.shape[1] != self.img_size[0] or img.shape[0] != self.img_size[1]:
            img = cv2.resize(img, (self.img_size[0], self.img_size[1]))
            
        return img
    
    def detect_cards(self, frame):
        """
        检测卡牌
        
        Args:
            frame (numpy.ndarray): 输入图像
            
        Returns:
            dict: 检测结果
        """
        if self.detector is None:
            return {'cards': []}
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行检测
        results = self.detector(frame)
        
        # 解析结果
        cards = []
        for result in results:
            boxes = result.boxes
            for i, box in enumerate(boxes):
                # 获取坐标和置信度
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                conf = box.conf[0].item()
                cls = int(box.cls[0].item())
                
                # 获取类别名称
                cls_name = result.names[cls]
                
                # 解析类别名称中的信息
                if '_' in cls_name:
                    parts = cls_name.split('_')
                    if len(parts) >= 2:
                        # 假设格式为 "group_id:类别名"
                        group_id = int(parts[0])
                        card_name = '_'.join(parts[1:])
                        
                        # 创建卡牌信息
                        card = {
                            'id': f"{i+1}_{card_name}",  # 简单ID生成
                            'group_id': group_id,
                            'pos': [x1, y1, x2-x1, y2-y1],
                            'conf': conf
                        }
                        cards.append(card)
        
        # 计算检测时间
        self.detection_time = time.time() - start_time
        
        return {'cards': cards}
    
    def process_frame(self, frame):
        """
        处理单帧

        Args:
            frame (numpy.ndarray): 输入图像

        Returns:
            tuple: (检测结果, 状态, 决策结果, 数字孪生结果)
        """
        # 检测卡牌
        detections = self.detect_cards(frame)

        # 🆕 数字孪生处理
        dt_result = None
        dt_start_time = time.time()

        try:
            # 使用YOLO检测器进行原始检测
            if self.detector is not None:
                yolo_results = self.detector(frame)

                # 转换为数字孪生系统格式
                formatted_detections = self._format_detections_for_digital_twin(yolo_results)

                if formatted_detections:
                    # 使用数字孪生统一主控器处理
                    dt_result = self.digital_twin_controller.process_frame(formatted_detections)

                    if dt_result.success:
                        self.dt_success_count += 1
                        print(f"✅ 数字孪生处理成功: {len(dt_result.processed_cards)}张卡牌")

                        # 获取处理统计
                        if hasattr(dt_result, 'statistics'):
                            stats = dt_result.statistics
                            print(f"📊 处理统计: 继承率={stats.get('inheritance_rate', 0):.1%}, "
                                  f"ID分配={stats.get('id_assignment_count', 0)}张")
                    else:
                        self.dt_failure_count += 1
                        print(f"❌ 数字孪生处理失败: {dt_result.validation_errors}")

        except Exception as e:
            self.dt_failure_count += 1
            print(f"❌ 数字孪生处理异常: {e}")

        # 计算数字孪生处理时间
        self.dt_processing_time = time.time() - dt_start_time

        # 转换为状态
        state = self.state_builder.yolo_to_rlcard_state(detections)

        # 记录开始时间
        start_time = time.time()

        # 做出决策
        action, confidence = self.decision_maker.make_decision(state)

        # 计算胜率
        win_rate = self.decision_maker.get_win_rate(state)

        # 格式化决策结果
        decision = self.decision_maker.format_decision(action, confidence, win_rate)

        # 计算决策时间
        self.decision_time = time.time() - start_time

        return detections, state, decision, dt_result
    
    def run(self, display=True):
        """
        运行主循环
        
        Args:
            display (bool): 是否显示结果
        """
        print("开始运行跑胡子AI...")
        print("按'q'键退出")
        
        while True:
            # 捕获屏幕
            frame = self.capture_screen()
            
            # 处理帧
            detections, state, decision, dt_result = self.process_frame(frame)
            
            # 更新性能统计
            self.frame_count += 1
            if time.time() - self.last_time >= 1.0:
                self.fps = self.frame_count
                self.frame_count = 0
                self.last_time = time.time()
            
            # 显示结果
            if display:
                # 绘制检测结果
                self._draw_detections(frame, detections)
                
                # 显示决策结果
                self._draw_decision(frame, decision)

                # 🆕 显示数字孪生结果
                self._draw_digital_twin_info(frame, dt_result)

                # 显示性能统计
                self._draw_stats(frame)
                
                # 显示图像
                cv2.imshow("跑胡子AI", frame)
                
                # 按'q'键退出
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
    
    def _draw_detections(self, frame, detections):
        """
        绘制检测结果
        
        Args:
            frame (numpy.ndarray): 输入图像
            detections (dict): 检测结果
        """
        for card in detections.get('cards', []):
            x, y, w, h = [int(v) for v in card['pos']]
            conf = card['conf']
            group_id = card.get('group_id', 0)
            card_id = card.get('id', '')
            
            # 根据group_id选择颜色
            colors = {
                1: (0, 255, 0),    # 手牌_观战方: 绿色
                2: (0, 255, 255),  # 调整手牌_观战方: 黄色
                3: (255, 0, 0),    # 抓牌_观战方: 蓝色
                4: (255, 0, 255),  # 打牌_观战方: 紫色
                5: (0, 0, 255),    # 弃牌_观战方: 红色
                6: (255, 255, 0),  # 吃碰区_观战方: 青色
                7: (128, 0, 0),    # 抓牌_对战方: 深蓝色
                8: (0, 128, 0),    # 打牌_对战方: 深绿色
                9: (0, 0, 128),    # 弃牌_对战方: 深红色
                16: (128, 128, 0), # 吃碰区_对战方: 深青色
            }
            color = colors.get(group_id, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # 绘制标签
            label = f"{card_id} ({conf:.2f})"
            cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    def _draw_decision(self, frame, decision):
        """
        绘制决策结果
        
        Args:
            frame (numpy.ndarray): 输入图像
            decision (dict): 决策结果
        """
        h, w = frame.shape[:2]
        
        # 绘制背景
        cv2.rectangle(frame, (w - 200, 0), (w, 100), (0, 0, 0), -1)
        
        # 绘制决策结果
        cv2.putText(frame, f"推荐: {decision.get('推荐动作', '无')}", (w - 190, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(frame, f"置信度: {decision.get('置信度', '0%')}", (w - 190, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(frame, f"胜率: {decision.get('胜率', '0%')}", (w - 190, 90),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    def _draw_digital_twin_info(self, frame, dt_result):
        """
        绘制数字孪生处理信息

        Args:
            frame (numpy.ndarray): 输入图像
            dt_result: 数字孪生处理结果
        """
        h, w = frame.shape[:2]

        # 绘制背景
        cv2.rectangle(frame, (w - 200, 110), (w, 200), (0, 0, 0), -1)

        if dt_result and dt_result.success:
            # 显示处理成功信息
            cv2.putText(frame, "数字孪生: 启用", (w - 190, 130),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            cv2.putText(frame, f"卡牌数: {len(dt_result.processed_cards)}", (w - 190, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            cv2.putText(frame, f"策略: {dt_result.strategy_used}", (w - 190, 170),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            cv2.putText(frame, f"耗时: {dt_result.processing_time*1000:.1f}ms", (w - 190, 190),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        else:
            # 显示处理失败或未启用信息
            status = "失败" if dt_result else "未启用"
            color = (0, 0, 255) if dt_result else (255, 255, 0)
            cv2.putText(frame, f"数字孪生: {status}", (w - 190, 130),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
    
    def _draw_stats(self, frame):
        """
        绘制性能统计
        
        Args:
            frame (numpy.ndarray): 输入图像
        """
        h, w = frame.shape[:2]
        
        # 绘制背景
        cv2.rectangle(frame, (0, 0), (200, 120), (0, 0, 0), -1)

        # 绘制性能统计
        cv2.putText(frame, f"FPS: {self.fps}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(frame, f"检测: {self.detection_time*1000:.1f}ms", (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(frame, f"数字孪生: {self.dt_processing_time*1000:.1f}ms", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(frame, f"成功/失败: {self.dt_success_count}/{self.dt_failure_count}", (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 计算成功率
        total_dt = self.dt_success_count + self.dt_failure_count
        success_rate = (self.dt_success_count / total_dt * 100) if total_dt > 0 else 0
        cv2.putText(frame, f"DT成功率: {success_rate:.1f}%", (10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="跑胡子AI")
    parser.add_argument('--config', type=str, default='src/config/config.json', help='配置文件路径')
    parser.add_argument('--no-display', action='store_true', help='不显示结果')
    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_args()
    
    # 创建跑胡子AI
    ai = PaohuziAI(args.config)
    
    # 运行
    ai.run(not args.no_display) 