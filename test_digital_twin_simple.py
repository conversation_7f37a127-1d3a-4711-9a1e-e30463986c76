#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数字孪生系统集成简化验证测试脚本

功能：
1. 验证统一主控器集成效果
2. 测试数字孪生处理能力
3. 生成测试报告
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

try:
    from src.core.digital_twin_controller import create_complete_controller
    print("✅ 成功导入数字孪生统一主控器")
except ImportError as e:
    print(f"❌ 导入数字孪生统一主控器失败: {e}")
    sys.exit(1)

class SimpleDigitalTwinTester:
    """简化数字孪生系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🔧 初始化数字孪生系统测试器...")
        
        # 初始化数字孪生统一主控器
        try:
            self.digital_twin_controller = create_complete_controller()
            print("✅ 数字孪生统一主控器初始化完成")
        except Exception as e:
            print(f"❌ 数字孪生统一主控器初始化失败: {e}")
            raise
        
        # 测试统计
        self.test_stats = {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "processing_times": [],
            "card_counts": [],
            "test_results": []
        }
    
    def create_mock_detections(self, scenario="basic"):
        """创建模拟检测数据"""
        scenarios = {
            "basic": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.95, 'group_id': 1},
                {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.90, 'group_id': 1},
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.88, 'group_id': 1}
            ],
            "complex": [
                # 观战方手牌区域
                {'label': '二', 'bbox': [100, 200, 150, 250], 'confidence': 0.95, 'group_id': 1},
                {'label': '三', 'bbox': [160, 200, 210, 250], 'confidence': 0.90, 'group_id': 1},
                {'label': '四', 'bbox': [220, 200, 270, 250], 'confidence': 0.88, 'group_id': 1},
                # 观战方打牌区域
                {'label': '五', 'bbox': [400, 200, 450, 250], 'confidence': 0.92, 'group_id': 4},
                # 对战方区域
                {'label': '六', 'bbox': [100, 50, 150, 100], 'confidence': 0.87, 'group_id': 7},
                {'label': '七', 'bbox': [160, 50, 210, 100], 'confidence': 0.85, 'group_id': 7}
            ],
            "empty": [],
            "single": [
                {'label': '八', 'bbox': [300, 150, 350, 200], 'confidence': 0.93, 'group_id': 1}
            ]
        }
        
        return scenarios.get(scenario, scenarios["basic"])
    
    def test_scenario(self, scenario_name, detections):
        """测试单个场景"""
        print(f"\n🔍 测试场景: {scenario_name}")
        print(f"📊 输入检测数据: {len(detections)} 个对象")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 数字孪生处理
            dt_result = self.digital_twin_controller.process_frame(detections)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 分析结果
            test_result = {
                "scenario": scenario_name,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "input_detection_count": len(detections),
                "dt_success": dt_result.success if dt_result else False,
                "processed_cards": len(dt_result.processed_cards) if dt_result and dt_result.success else 0,
                "strategy_used": dt_result.strategy_used if dt_result else "未知",
                "validation_errors": dt_result.validation_errors if dt_result else [],
                "validation_warnings": dt_result.validation_warnings if dt_result else [],
                "statistics": dt_result.statistics if dt_result and hasattr(dt_result, 'statistics') else {}
            }
            
            # 更新统计
            self.test_stats["total_tests"] += 1
            self.test_stats["processing_times"].append(processing_time)
            self.test_stats["card_counts"].append(test_result["processed_cards"])
            
            if test_result["dt_success"]:
                self.test_stats["successful_tests"] += 1
                print(f"✅ 数字孪生处理成功")
                print(f"📈 处理卡牌: {test_result['processed_cards']}张")
                print(f"📈 策略: {test_result['strategy_used']}")
                print(f"📈 耗时: {processing_time*1000:.1f}ms")
                
                # 显示处理后的卡牌信息
                if dt_result.processed_cards:
                    print("🎯 处理后的卡牌:")
                    for i, card in enumerate(dt_result.processed_cards[:3]):  # 只显示前3张
                        twin_id = card.get('twin_id', '未分配')
                        label = card.get('label', '未知')
                        group_id = card.get('group_id', '未知')
                        print(f"   {i+1}. {label} -> {twin_id} (区域{group_id})")
                    if len(dt_result.processed_cards) > 3:
                        print(f"   ... 还有{len(dt_result.processed_cards)-3}张卡牌")
                        
            else:
                self.test_stats["failed_tests"] += 1
                print(f"❌ 数字孪生处理失败")
                if test_result['validation_errors']:
                    print(f"❌ 验证错误: {test_result['validation_errors']}")
                if test_result['validation_warnings']:
                    print(f"⚠️ 验证警告: {test_result['validation_warnings']}")
            
            self.test_stats["test_results"].append(test_result)
            return test_result
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            self.test_stats["total_tests"] += 1
            self.test_stats["failed_tests"] += 1
            
            error_result = {
                "scenario": scenario_name,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "dt_success": False
            }
            self.test_stats["test_results"].append(error_result)
            return error_result
    
    def run_all_tests(self):
        """运行所有测试场景"""
        print("\n🎯 开始运行所有测试场景...")
        
        # 测试场景列表
        test_scenarios = [
            ("基础场景", "basic"),
            ("复杂场景", "complex"),
            ("空场景", "empty"),
            ("单卡牌场景", "single")
        ]
        
        for scenario_name, scenario_key in test_scenarios:
            detections = self.create_mock_detections(scenario_key)
            self.test_scenario(scenario_name, detections)
            
            # 短暂延迟，模拟真实处理间隔
            time.sleep(0.1)
    
    def test_system_status(self):
        """测试系统状态查询"""
        print("\n🔍 测试系统状态查询...")
        
        try:
            # 获取系统状态
            status = self.digital_twin_controller.get_system_status()
            print("✅ 系统状态查询成功")
            print(f"📊 系统状态: {status}")
            
            # 获取性能统计
            perf_stats = self.digital_twin_controller.get_performance_stats()
            print("✅ 性能统计查询成功")
            print(f"📊 性能统计: {perf_stats}")
            
        except Exception as e:
            print(f"❌ 系统状态查询失败: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 生成测试报告...")
        
        # 计算统计指标
        total_tests = self.test_stats["total_tests"]
        success_rate = (self.test_stats["successful_tests"] / total_tests * 100) if total_tests > 0 else 0
        avg_processing_time = sum(self.test_stats["processing_times"]) / len(self.test_stats["processing_times"]) if self.test_stats["processing_times"] else 0
        avg_card_count = sum(self.test_stats["card_counts"]) / len(self.test_stats["card_counts"]) if self.test_stats["card_counts"] else 0
        
        report = {
            "test_summary": {
                "timestamp": datetime.now().isoformat(),
                "total_tests": total_tests,
                "successful_tests": self.test_stats["successful_tests"],
                "failed_tests": self.test_stats["failed_tests"],
                "success_rate": f"{success_rate:.1f}%",
                "average_processing_time": f"{avg_processing_time*1000:.1f}ms",
                "average_card_count": f"{avg_card_count:.1f}张"
            },
            "performance_metrics": {
                "min_processing_time": f"{min(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "max_processing_time": f"{max(self.test_stats['processing_times'])*1000:.1f}ms" if self.test_stats["processing_times"] else "N/A",
                "total_cards_processed": sum(self.test_stats["card_counts"])
            },
            "detailed_results": self.test_stats["test_results"]
        }
        
        # 保存报告
        os.makedirs("output", exist_ok=True)
        report_path = "output/digital_twin_simple_test_report.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试报告已保存: {report_path}")
        
        # 打印摘要
        print("\n📊 测试摘要:")
        print(f"总测试数: {total_tests}")
        print(f"成功: {self.test_stats['successful_tests']}, 失败: {self.test_stats['failed_tests']}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均处理时间: {avg_processing_time*1000:.1f}ms")
        print(f"平均卡牌数: {avg_card_count:.1f}张")
        
        return report

def main():
    """主函数"""
    print("🚀 数字孪生系统集成简化验证测试")
    print("=" * 50)
    
    try:
        # 创建测试器
        tester = SimpleDigitalTwinTester()
        
        # 运行所有测试场景
        tester.run_all_tests()
        
        # 测试系统状态
        tester.test_system_status()
        
        # 生成测试报告
        report = tester.generate_test_report()
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
